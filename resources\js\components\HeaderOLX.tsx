import CategoryNavigation from '@/components/CategoryNavigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Category } from '@/types';
import { Link, router } from '@inertiajs/react';
import {
    Bell,
    MapPin,
    Menu,
    MessageCircle,
    Plus,
    Search,
    User,
} from 'lucide-react';
import { useState } from 'react';
import { SiLaravel } from 'react-icons/si';

interface HeaderProps {
    categories?: Category[];
    auth?: {
        user?: {
            id: number;
            name: string;
            email: string;
        };
    };
}

export default function MainHeader({ categories = [], auth }: HeaderProps) {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [notificationCount] = useState(0);
    const isAuthenticated = !!auth?.user;

    const handleSearch = () => {
        if (searchQuery.trim()) {
            router.get('/buscar', { q: searchQuery });
            setSearchQuery('');
        }
    };

    return (
        <header className="w-full bg-white shadow-sm">
            {/* Main Header */}
            <div className="border-b border-gray-200">
                <div className="container mx-auto px-4">
                    <div className="flex h-16 items-center justify-between">
                        {/* Logo */}
                        <div className="flex items-center space-x-8">
                            <Link href="/" className="flex items-center space-x-2">
                                <SiLaravel className="h-8 w-8 text-red-500" />
                                <span className="text-xl font-bold text-gray-800">Marketplace</span>
                            </Link>
                        </div>

                        {/* Search Bar - Desktop */}
                        <div className="mx-8 hidden max-w-2xl flex-1 md:flex">
                            <div className="relative flex-1">
                                <Input
                                    type="text"
                                    placeholder="Buscar 'Aparta'"
                                    value={searchQuery}
                                    onChange={(e) =>
                                        setSearchQuery(e.target.value)
                                    }
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            handleSearch();
                                        }
                                    }}
                                    className="h-12 rounded-l-md rounded-r-none border-gray-300 pr-12 pl-4 focus:border-primary focus:ring-primary"
                                />
                                <Button
                                    size="sm"
                                    onClick={handleSearch}
                                    className="absolute top-0 right-0 h-12 rounded-l-none bg-primary px-6 hover:bg-primary/90"
                                >
                                    <Search className="h-5 w-5" />
                                </Button>
                            </div>
                        </div>

                        {/* Location and Actions */}
                        <div className="hidden items-center space-x-4 md:flex">
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-gray-600 hover:text-primary"
                            >
                                <MapPin className="mr-1 h-4 w-4" />
                                RJ
                            </Button>

                            {isAuthenticated ? (
                                <>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => router.get('/chat')}
                                        className="text-gray-600 hover:text-primary"
                                    >
                                        <MessageCircle className="h-5 w-5" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                            router.get('/notificacoes')
                                        }
                                        className="relative text-gray-600 hover:text-primary"
                                    >
                                        <Bell className="h-5 w-5" />
                                        {notificationCount > 0 && (
                                            <Badge className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 p-0 text-xs text-white">
                                                {notificationCount}
                                            </Badge>
                                        )}
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => router.get('/perfil')}
                                        className="text-gray-600 hover:text-primary"
                                    >
                                        <User className="h-5 w-5" />
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/anunciar')}
                                        className="bg-primary px-6 text-white hover:bg-primary/90"
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Anunciar grátis
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => router.get('/login')}
                                        className="text-gray-600 hover:text-primary"
                                    >
                                        Entrar
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/register')}
                                        className="bg-primary px-6 text-white hover:bg-primary/90"
                                    >
                                        Cadastrar
                                    </Button>
                                </>
                            )}
                        </div>

                        {/* Mobile menu button */}
                        <div className="flex items-center md:hidden">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => setIsMenuOpen(!isMenuOpen)}
                            >
                                <Menu className="h-6 w-6" />
                                <span className="sr-only">Abrir menu</span>
                            </Button>
                        </div>
                    </div>

                    {/* Mobile Search */}
                    <div className="py-3 md:hidden">
                        <div className="relative">
                            <Input
                                type="text"
                                placeholder="Buscar 'Aparta'"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        handleSearch();
                                    }
                                }}
                                className="h-10 border-gray-300 pr-12 pl-4 focus:border-primary focus:ring-primary"
                            />
                            <Button
                                size="sm"
                                onClick={handleSearch}
                                className="absolute top-1 right-1 h-8 bg-primary px-3 hover:bg-primary/90"
                            >
                                <Search className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Category Navigation */}
            <CategoryNavigation categories={categories} />

            {/* Mobile Navigation Menu */}
            {isMenuOpen && (
                <div className="border-t border-gray-100 bg-white py-4 md:hidden">
                    <div className="container mx-auto px-4">
                        <nav className="flex flex-col space-y-2">
                            {isAuthenticated ? (
                                <>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/meus-anuncios')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Meus Anúncios
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() => router.get('/chat')}
                                        className="justify-start text-gray-600"
                                    >
                                        Chat
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        onClick={() =>
                                            router.get('/notificacoes')
                                        }
                                        className="justify-start text-gray-600"
                                    >
                                        Notificações
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/anunciar')}
                                        className="mt-4 justify-start bg-primary text-white hover:bg-primary/90"
                                    >
                                        <Plus className="mr-2 h-4 w-4" />
                                        Anunciar grátis
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button
                                        variant="ghost"
                                        onClick={() => router.get('/login')}
                                        className="justify-start text-gray-600"
                                    >
                                        Entrar
                                    </Button>
                                    <Button
                                        onClick={() => router.get('/register')}
                                        className="justify-start bg-primary text-white hover:bg-primary/90"
                                    >
                                        Cadastrar
                                    </Button>
                                </>
                            )}
                        </nav>
                    </div>
                </div>
            )}
        </header>
    );
}
