import FeaturedListings from '@/components/FeaturedListings';
import HeroCarousel from '@/components/HeroCarousel';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
interface Category {
    id: number;
    name: string;
    slug: string;
    description: string;
    icon?: string;
    image?: string;
    url: string;
    vehicles_count?: number;
    parts_count?: number;
}

interface Vehicle {
    id: number;
    model: string;
    slug: string;
    price: number;
    promotional_price?: number;
    year_manufacture: number;
    mileage: number;
    color: string;
    fuel_type: string;
    transmission: string;
    is_featured: boolean;
    is_negotiable: boolean;
    main_image_url?: string;
    url: string;
    brand: {
        name: string;
    };
    category: {
        name: string;
        slug: string;
    };
}

interface Part {
    id: number;
    name: string;
    slug: string;
    price: number;
    promotional_price?: number;
    stock_quantity: number;
    is_original: boolean;
    is_featured: boolean;
    main_image_url?: string;
    url: string;
    brand: {
        name: string;
    };
}

interface WelcomeProps extends PageProps {
    categories: Category[];
    featuredCars?: Vehicle[];
    featuredMotos?: Vehicle[];
    featuredParts?: Part[];
}

export default function Welcome({
    categories = [],
    featuredCars = [],
    featuredMotos = [],
    featuredParts = [],
}: WelcomeProps) {
    return (
        <MainLayout categories={categories}>
            <div className="space-y-0">
                <HeroCarousel />

                <div className="container mx-auto space-y-12 px-4 py-12">
                    {/* Carrossel de Carros */}
                    <FeaturedListings
                        vehicles={featuredCars}
                        title="Carros em Destaque"
                        showMoreLink="/pesquisar?categoria=carros"
                    />

                    {/* Carrossel de Motos */}
                    <FeaturedListings
                        vehicles={featuredMotos}
                        title="Motos em Destaque"
                        showMoreLink="/pesquisar?categoria=motos"
                    />

                    {/* Carrossel de Peças */}
                    <FeaturedListings
                        parts={featuredParts}
                        title="Peças em Destaque"
                        showMoreLink="/pesquisar?categoria=pecas"
                    />
                </div>
            </div>
        </MainLayout>
    );
}
